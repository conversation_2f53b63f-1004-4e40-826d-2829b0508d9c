package com.cnpc.xn.cd.kfsc.measure.controller;

import com.cnpc.xn.cd.kfsc.measure.dto.WellStimTrackingQueryDto;
import com.cnpc.xn.cd.kfsc.measure.service.WellStimPlanService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 措施计划管理控制器测试类
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@WebMvcTest(WellStimPlanController.class)
public class WellStimPlanControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WellStimPlanService wellStimPlanService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetTrackingPage() throws Exception {
        WellStimTrackingQueryDto queryDto = new WellStimTrackingQueryDto();
        queryDto.setCurrentPage(1);
        queryDto.setPageSize(10);
        queryDto.setPlanYearMonth("202501");

        mockMvc.perform(post("/api/v1/well-stim-plan/tracking/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDto)))
                .andExpect(status().isOk());
    }
}
