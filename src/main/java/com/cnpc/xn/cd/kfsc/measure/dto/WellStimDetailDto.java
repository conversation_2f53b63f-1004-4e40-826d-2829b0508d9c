package com.cnpc.xn.cd.kfsc.measure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 措施计划明细DTO
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = "WellStimDetailDto", description = "措施计划明细传输对象")
public class WellStimDetailDto {

    @ApiModelProperty("明细ID")
    private String detailId;

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty("预计开工月份")
    private String monthNum;

    @ApiModelProperty(value = "井ID", required = true)
    @NotBlank(message = "井ID不能为空")
    private String wellId;

    @ApiModelProperty(value = "措施分类代码", required = true)
    @NotBlank(message = "措施分类代码不能为空")
    private String stimKeyId;

    @ApiModelProperty("措施计划实施时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planImplementTime;

    @ApiModelProperty("实际开工日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructStartDate;

    @ApiModelProperty("施工完成日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructEndDate;

    @ApiModelProperty("采气工艺投运日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionDate;

    @ApiModelProperty("措施前日产气量")
    private BigDecimal gasProductionPreStimDaily;

    @ApiModelProperty("措施后日产气量")
    private BigDecimal gasProductionAftStimDaily;

    @ApiModelProperty("开展情况动态")
    private String progressImplement;

    @ApiModelProperty("安排类型")
    private String changeType;

    @ApiModelProperty("备注")
    private String remarks;
}
