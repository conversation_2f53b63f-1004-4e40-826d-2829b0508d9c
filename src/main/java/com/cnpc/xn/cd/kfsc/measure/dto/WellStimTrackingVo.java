package com.cnpc.xn.cd.kfsc.measure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 措施计划跟踪展示VO
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = "WellStimTrackingVo", description = "措施计划跟踪展示对象")
public class WellStimTrackingVo {

    @ApiModelProperty("明细ID")
    private String detailId;

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty("序号")
    private Integer serialNumber;

    @ApiModelProperty("年度计划年月")
    private String planYearMonth;

    @ApiModelProperty("预计开工月份")
    private String monthNum;

    @ApiModelProperty("单位名称")
    private String orgName;

    @ApiModelProperty("领域")
    private String domain;

    @ApiModelProperty("井号")
    private String wellName;

    @ApiModelProperty("措施类型名称")
    private String stimTypeName;

    @ApiModelProperty("资金渠道")
    private String fundingChannel;

    @ApiModelProperty("开展情况动态")
    private String progressImplement;

    @ApiModelProperty("实际开工日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date constructStartDate;

    @ApiModelProperty("施工完成日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date constructEndDate;

    @ApiModelProperty("采气工艺投运日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date completionDate;

    @ApiModelProperty("措施前日产气量")
    private BigDecimal gasProductionPreStimDaily;

    @ApiModelProperty("措施后日产气量")
    private BigDecimal gasProductionAftStimDaily;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("维护人")
    private String maintainerName;

    @ApiModelProperty("维护时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
