package com.cnpc.xn.cd.kfsc.measure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 措施计划明细表
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@TableName("PC_WELL_STIM_DETAIL")
@ApiModel(value = "PcWellStimDetail对象", description = "措施计划明细表")
public class PcWellStimDetail {

    @ApiModelProperty("明细ID")
    @TableId("DETAIL_ID")
    private String detailId;

    @ApiModelProperty("措施计划主键ID")
    @TableField("PLAN_ID")
    private String planId;

    @ApiModelProperty("预计开工月份")
    @TableField("MONTH_NUM")
    private String monthNum;

    @ApiModelProperty("井ID")
    @TableField("WELL_ID")
    private String wellId;

    @ApiModelProperty("措施分类代码")
    @TableField("STIM_KEY_ID")
    private String stimKeyId;

    @ApiModelProperty("措施计划实施时间")
    @TableField("PLAN_IMPLEMENT_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planImplementTime;

    @ApiModelProperty("实际开工日期")
    @TableField("CONSTRUCT_START_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructStartDate;

    @ApiModelProperty("施工完成日期")
    @TableField("CONSTRUCT_END_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructEndDate;

    @ApiModelProperty("采气工艺投运日期")
    @TableField("COMPLETION_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionDate;

    @ApiModelProperty("措施前日产气量")
    @TableField("GAS_PROD_PRE_STIM_DAILY")
    private BigDecimal gasProductionPreStimDaily;

    @ApiModelProperty("措施后日产气量")
    @TableField("GAS_PROD_AFT_STIM_DAILY")
    private BigDecimal gasProductionAftStimDaily;

    @ApiModelProperty("开展情况动态")
    @TableField("PROGRESS_IMPLEMENT")
    private String progressImplement;

    @ApiModelProperty("安排类型")
    @TableField("CHANGE_TYPE")
    private String changeType;

    @ApiModelProperty("备注")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty("创建用户ID")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("创建时间")
    @TableField("CREATE_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty("更新用户ID")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("更新时间")
    @TableField("UPDATE_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty("来源")
    @TableField("WELL_SOURCE")
    private String wellSource;
}
