package com.cnpc.xn.cd.kfsc.measure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.kfsc.measure.dto.*;
import com.cnpc.xn.cd.kfsc.measure.entity.PcWellStimPlan;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 措施计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface WellStimPlanService {

    /**
     * 分页查询措施计划跟踪列表
     * 
     * @param queryDto 查询条件
     * @return 分页结果
     */
    Page<WellStimTrackingVo> getTrackingPage(WellStimTrackingQueryDto queryDto);

    /**
     * 更新跟踪信息
     * 
     * @param updateDto 更新数据
     * @return 是否成功
     */
    boolean updateTracking(WellStimTrackingUpdateDto updateDto);

    /**
     * 创建年计划
     * 
     * @param planDto 计划数据
     * @return 计划ID
     */
    String createAnnualPlan(WellStimPlanDto planDto);

    /**
     * 创建月计划
     * 
     * @param planDto 计划数据
     * @return 计划ID
     */
    String createMonthlyPlan(WellStimPlanDto planDto);

    /**
     * 分页查询年计划列表
     * 
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @param planYear 计划年份
     * @param orgId 单位ID
     * @param planStatus 计划状态
     * @return 分页结果
     */
    Page<PcWellStimPlan> getAnnualPlanPage(Integer currentPage, Integer pageSize, 
                                          String planYear, String orgId, String planStatus);

    /**
     * 分页查询月计划列表
     * 
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @param planYearMonth 计划年月
     * @param orgId 单位ID
     * @return 分页结果
     */
    Page<PcWellStimPlan> getMonthlyPlanPage(Integer currentPage, Integer pageSize, 
                                           String planYearMonth, String orgId);

    /**
     * 提交计划
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean submitPlan(String planId);

    /**
     * 导入措施计划数据
     * 
     * @param file Excel文件
     * @param planId 计划ID
     * @return 导入结果
     */
    Map<String, Object> importData(MultipartFile file, String planId);

    /**
     * 导出措施计划数据
     * 
     * @param queryDto 查询条件
     * @param response HTTP响应
     */
    void exportData(WellStimTrackingQueryDto queryDto, HttpServletResponse response);

    /**
     * 获取单位列表
     * 
     * @return 单位列表
     */
    List<Map<String, Object>> getOrganizations();

    /**
     * 获取井号列表
     * 
     * @param orgId 单位ID
     * @return 井号列表
     */
    List<Map<String, Object>> getWells(String orgId);

    /**
     * 获取措施类型列表
     * 
     * @return 措施类型列表
     */
    List<Map<String, Object>> getStimTypes();

    /**
     * 获取已提交的年计划
     * 
     * @param orgId 单位ID
     * @return 年计划列表
     */
    List<Map<String, Object>> getSubmittedAnnualPlans(String orgId);
}
