package com.cnpc.xn.cd.kfsc.measure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 措施计划跟踪更新DTO
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = "WellStimTrackingUpdateDto", description = "措施计划跟踪更新对象")
public class WellStimTrackingUpdateDto {

    @ApiModelProperty(value = "明细ID", required = true)
    @NotBlank(message = "明细ID不能为空")
    private String detailId;

    @ApiModelProperty("开展情况动态")
    private String progressImplement;

    @ApiModelProperty("实际开工日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructStartDate;

    @ApiModelProperty("施工完成日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date constructEndDate;

    @ApiModelProperty("采气工艺投运日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionDate;

    @ApiModelProperty("措施前日产气量")
    private BigDecimal gasProductionPreStimDaily;

    @ApiModelProperty("措施后日产气量")
    private BigDecimal gasProductionAftStimDaily;

    @ApiModelProperty("备注")
    private String remarks;
}
