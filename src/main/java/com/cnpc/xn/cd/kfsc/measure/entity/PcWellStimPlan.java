package com.cnpc.xn.cd.kfsc.measure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 措施工艺计划主表
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@TableName("PC_WELL_STIM_PLAN")
@ApiModel(value = "PcWellStimPlan对象", description = "措施工艺计划主表")
public class PcWellStimPlan {

    @ApiModelProperty("措施计划主键ID")
    @TableId("PLAN_ID")
    private String planId;

    @ApiModelProperty("计划名称")
    @TableField("PLAN_NAME")
    private String planName;

    @ApiModelProperty("单位ID")
    @TableField("PLAN_ORG_ID")
    private String planOrgId;

    @ApiModelProperty("年度计划年月（例如202501）")
    @TableField("PLAN_YEAR_MONTH")
    private String planYearMonth;

    @ApiModelProperty("计划类型")
    @TableField("PLAN_TYPE")
    private String planType;

    @ApiModelProperty("计划状态：0 保存未提交，1 已提交审核中，2 审核通过，5 审核未通过")
    @TableField("PLAN_STATUS")
    private String planStatus;

    @ApiModelProperty("备注")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty("创建人ID")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("创建日期")
    @TableField("CREATE_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty("更新用户ID")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("更新日期")
    @TableField("UPDATE_DATE")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
