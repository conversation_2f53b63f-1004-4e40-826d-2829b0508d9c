package com.cnpc.xn.cd.kfsc.measure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.kfsc.measure.dto.*;
import com.cnpc.xn.cd.kfsc.measure.entity.PcWellStimDetail;
import com.cnpc.xn.cd.kfsc.measure.entity.PcWellStimPlan;
import com.cnpc.xn.cd.kfsc.measure.mapper.PcWellStimDetailMapper;
import com.cnpc.xn.cd.kfsc.measure.mapper.PcWellStimPlanMapper;
import com.cnpc.xn.cd.kfsc.measure.service.WellStimPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 措施计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Slf4j
@Service
public class WellStimPlanServiceImpl implements WellStimPlanService {

    @Autowired
    private PcWellStimPlanMapper planMapper;

    @Autowired
    private PcWellStimDetailMapper detailMapper;

    @Override
    public Page<WellStimTrackingVo> getTrackingPage(WellStimTrackingQueryDto queryDto) {
        Page<WellStimTrackingVo> page = new Page<>(queryDto.getCurrentPage(), queryDto.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<PcWellStimDetail> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(queryDto.getPlanYearMonth())) {
            // 通过关联查询计划表获取年月条件
            wrapper.exists("SELECT 1 FROM PC_WELL_STIM_PLAN p WHERE p.PLAN_ID = PC_WELL_STIM_DETAIL.PLAN_ID AND p.PLAN_YEAR_MONTH = {0}", 
                          queryDto.getPlanYearMonth());
        }
        
        if (StringUtils.hasText(queryDto.getOrgId())) {
            wrapper.exists("SELECT 1 FROM PC_WELL_STIM_PLAN p WHERE p.PLAN_ID = PC_WELL_STIM_DETAIL.PLAN_ID AND p.PLAN_ORG_ID = {0}", 
                          queryDto.getOrgId());
        }
        
        if (StringUtils.hasText(queryDto.getWellId())) {
            wrapper.eq(PcWellStimDetail::getWellId, queryDto.getWellId());
        }
        
        if (StringUtils.hasText(queryDto.getStimKeyId())) {
            wrapper.eq(PcWellStimDetail::getStimKeyId, queryDto.getStimKeyId());
        }
        
        if (StringUtils.hasText(queryDto.getProgressImplement())) {
            wrapper.eq(PcWellStimDetail::getProgressImplement, queryDto.getProgressImplement());
        }
        
        // 处理动态查询条件
        if (queryDto.getQueryConditions() != null && !queryDto.getQueryConditions().isEmpty()) {
            for (WellStimTrackingQueryDto.QueryCondition condition : queryDto.getQueryConditions()) {
                applyQueryCondition(wrapper, condition);
            }
        }
        
        wrapper.orderByDesc(PcWellStimDetail::getUpdateDate);
        
        // 这里应该调用自定义的mapper方法来进行关联查询
        // 为了简化示例，这里返回空的分页结果
        return detailMapper.selectTrackingPage(page, queryDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTracking(WellStimTrackingUpdateDto updateDto) {
        PcWellStimDetail detail = detailMapper.selectById(updateDto.getDetailId());
        if (detail == null) {
            throw new RuntimeException("明细记录不存在");
        }
        
        // 更新字段
        if (StringUtils.hasText(updateDto.getProgressImplement())) {
            detail.setProgressImplement(updateDto.getProgressImplement());
        }
        if (updateDto.getConstructStartDate() != null) {
            detail.setConstructStartDate(updateDto.getConstructStartDate());
        }
        if (updateDto.getConstructEndDate() != null) {
            detail.setConstructEndDate(updateDto.getConstructEndDate());
        }
        if (updateDto.getCompletionDate() != null) {
            detail.setCompletionDate(updateDto.getCompletionDate());
        }
        if (updateDto.getGasProductionPreStimDaily() != null) {
            detail.setGasProductionPreStimDaily(updateDto.getGasProductionPreStimDaily());
        }
        if (updateDto.getGasProductionAftStimDaily() != null) {
            detail.setGasProductionAftStimDaily(updateDto.getGasProductionAftStimDaily());
        }
        if (StringUtils.hasText(updateDto.getRemarks())) {
            detail.setRemarks(updateDto.getRemarks());
        }
        
        detail.setUpdateDate(new Date());
        // TODO: 设置更新用户
        
        return detailMapper.updateById(detail) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAnnualPlan(WellStimPlanDto planDto) {
        // 检查年份+单位唯一性
        LambdaQueryWrapper<PcWellStimPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcWellStimPlan::getPlanOrgId, planDto.getPlanOrgId())
               .eq(PcWellStimPlan::getPlanYearMonth, planDto.getPlanYear())
               .eq(PcWellStimPlan::getPlanType, "ANNUAL");
        
        if (planMapper.selectCount(wrapper) > 0) {
            throw new RuntimeException("该单位的年度计划已存在");
        }
        
        // 创建主表记录
        PcWellStimPlan plan = new PcWellStimPlan();
        BeanUtils.copyProperties(planDto, plan);
        plan.setPlanId(UUID.randomUUID().toString().replace("-", ""));
        plan.setPlanYearMonth(planDto.getPlanYear());
        plan.setPlanStatus("0"); // 保存未提交
        plan.setCreateDate(new Date());
        plan.setUpdateDate(new Date());
        // TODO: 设置创建用户
        
        planMapper.insert(plan);
        
        // 创建明细记录
        for (WellStimDetailDto detailDto : planDto.getDetails()) {
            PcWellStimDetail detail = new PcWellStimDetail();
            BeanUtils.copyProperties(detailDto, detail);
            detail.setDetailId(UUID.randomUUID().toString().replace("-", ""));
            detail.setPlanId(plan.getPlanId());
            detail.setCreateDate(new Date());
            detail.setUpdateDate(new Date());
            // TODO: 设置创建用户
            
            detailMapper.insert(detail);
        }
        
        return plan.getPlanId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMonthlyPlan(WellStimPlanDto planDto) {
        // 检查基础年计划是否存在且已提交
        PcWellStimPlan basePlan = planMapper.selectById(planDto.getBasePlanId());
        if (basePlan == null) {
            throw new RuntimeException("基础年计划不存在");
        }
        if (!"2".equals(basePlan.getPlanStatus())) {
            throw new RuntimeException("基础年计划未审核通过");
        }
        
        // 创建主表记录
        PcWellStimPlan plan = new PcWellStimPlan();
        BeanUtils.copyProperties(planDto, plan);
        plan.setPlanId(UUID.randomUUID().toString().replace("-", ""));
        plan.setPlanStatus("0"); // 保存未提交
        plan.setCreateDate(new Date());
        plan.setUpdateDate(new Date());
        // TODO: 设置创建用户
        
        planMapper.insert(plan);
        
        // 创建明细记录
        for (WellStimDetailDto detailDto : planDto.getDetails()) {
            PcWellStimDetail detail = new PcWellStimDetail();
            BeanUtils.copyProperties(detailDto, detail);
            detail.setDetailId(UUID.randomUUID().toString().replace("-", ""));
            detail.setPlanId(plan.getPlanId());
            detail.setCreateDate(new Date());
            detail.setUpdateDate(new Date());
            // TODO: 设置创建用户
            
            detailMapper.insert(detail);
        }
        
        return plan.getPlanId();
    }

    @Override
    public Page<PcWellStimPlan> getAnnualPlanPage(Integer currentPage, Integer pageSize, 
                                                 String planYear, String orgId, String planStatus) {
        Page<PcWellStimPlan> page = new Page<>(currentPage, pageSize);
        
        LambdaQueryWrapper<PcWellStimPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcWellStimPlan::getPlanType, "ANNUAL");
        
        if (StringUtils.hasText(planYear)) {
            wrapper.eq(PcWellStimPlan::getPlanYearMonth, planYear);
        }
        if (StringUtils.hasText(orgId)) {
            wrapper.eq(PcWellStimPlan::getPlanOrgId, orgId);
        }
        if (StringUtils.hasText(planStatus)) {
            wrapper.eq(PcWellStimPlan::getPlanStatus, planStatus);
        }
        
        wrapper.orderByDesc(PcWellStimPlan::getCreateDate);
        
        return planMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<PcWellStimPlan> getMonthlyPlanPage(Integer currentPage, Integer pageSize, 
                                                  String planYearMonth, String orgId) {
        Page<PcWellStimPlan> page = new Page<>(currentPage, pageSize);
        
        LambdaQueryWrapper<PcWellStimPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcWellStimPlan::getPlanType, "MONTHLY");
        
        if (StringUtils.hasText(planYearMonth)) {
            wrapper.eq(PcWellStimPlan::getPlanYearMonth, planYearMonth);
        }
        if (StringUtils.hasText(orgId)) {
            wrapper.eq(PcWellStimPlan::getPlanOrgId, orgId);
        }
        
        wrapper.orderByDesc(PcWellStimPlan::getCreateDate);
        
        return planMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitPlan(String planId) {
        PcWellStimPlan plan = planMapper.selectById(planId);
        if (plan == null) {
            throw new RuntimeException("计划不存在");
        }
        if (!"0".equals(plan.getPlanStatus())) {
            throw new RuntimeException("计划状态不允许提交");
        }
        
        plan.setPlanStatus("1"); // 已提交审核中
        plan.setUpdateDate(new Date());
        // TODO: 设置更新用户
        
        return planMapper.updateById(plan) > 0;
    }

    @Override
    public Map<String, Object> importData(MultipartFile file, String planId) {
        // TODO: 实现Excel导入逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", 0);
        result.put("failCount", 0);
        result.put("errorMessages", new ArrayList<>());
        return result;
    }

    @Override
    public void exportData(WellStimTrackingQueryDto queryDto, HttpServletResponse response) {
        // TODO: 实现Excel导出逻辑
    }

    @Override
    public List<Map<String, Object>> getOrganizations() {
        // TODO: 调用组织机构服务获取单位列表
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getWells(String orgId) {
        // TODO: 根据单位ID获取井号列表
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getStimTypes() {
        // TODO: 获取措施类型列表
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getSubmittedAnnualPlans(String orgId) {
        LambdaQueryWrapper<PcWellStimPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcWellStimPlan::getPlanType, "ANNUAL")
               .eq(PcWellStimPlan::getPlanStatus, "2"); // 审核通过
        
        if (StringUtils.hasText(orgId)) {
            wrapper.eq(PcWellStimPlan::getPlanOrgId, orgId);
        }
        
        List<PcWellStimPlan> plans = planMapper.selectList(wrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (PcWellStimPlan plan : plans) {
            Map<String, Object> item = new HashMap<>();
            item.put("planId", plan.getPlanId());
            item.put("planName", plan.getPlanName());
            item.put("planYear", plan.getPlanYearMonth());
            item.put("orgId", plan.getPlanOrgId());
            // TODO: 获取单位名称
            item.put("orgName", "");
            result.add(item);
        }
        
        return result;
    }

    /**
     * 应用查询条件
     */
    private void applyQueryCondition(LambdaQueryWrapper<PcWellStimDetail> wrapper, 
                                   WellStimTrackingQueryDto.QueryCondition condition) {
        String paramType = condition.getParamType();
        String operator = condition.getOperator();
        String value = condition.getValue();
        
        if (!StringUtils.hasText(paramType) || !StringUtils.hasText(operator) || !StringUtils.hasText(value)) {
            return;
        }
        
        // 根据参数类型和操作符构建查询条件
        switch (paramType) {
            case "实际开工日期":
                applyDateCondition(wrapper, PcWellStimDetail::getConstructStartDate, operator, value);
                break;
            case "施工完成日期":
                applyDateCondition(wrapper, PcWellStimDetail::getConstructEndDate, operator, value);
                break;
            case "采气工艺投运日期":
                applyDateCondition(wrapper, PcWellStimDetail::getCompletionDate, operator, value);
                break;
            case "措施前日产气量":
                applyNumberCondition(wrapper, PcWellStimDetail::getGasProductionPreStimDaily, operator, value);
                break;
            case "措施后日产气量":
                applyNumberCondition(wrapper, PcWellStimDetail::getGasProductionAftStimDaily, operator, value);
                break;
            default:
                break;
        }
    }

    /**
     * 应用日期条件
     */
    private void applyDateCondition(LambdaQueryWrapper<PcWellStimDetail> wrapper, 
                                  com.baomidou.mybatisplus.core.toolkit.support.SFunction<PcWellStimDetail, Date> column,
                                  String operator, String value) {
        try {
            Date dateValue = java.sql.Date.valueOf(value);
            switch (operator) {
                case "大于":
                    wrapper.gt(column, dateValue);
                    break;
                case "等于":
                    wrapper.eq(column, dateValue);
                    break;
                case "小于":
                    wrapper.lt(column, dateValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.warn("日期格式错误: {}", value);
        }
    }

    /**
     * 应用数值条件
     */
    private void applyNumberCondition(LambdaQueryWrapper<PcWellStimDetail> wrapper,
                                    com.baomidou.mybatisplus.core.toolkit.support.SFunction<PcWellStimDetail, ?> column,
                                    String operator, String value) {
        try {
            Double numberValue = Double.valueOf(value);
            switch (operator) {
                case "大于":
                    wrapper.gt(column, numberValue);
                    break;
                case "等于":
                    wrapper.eq(column, numberValue);
                    break;
                case "小于":
                    wrapper.lt(column, numberValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.warn("数值格式错误: {}", value);
        }
    }
}
