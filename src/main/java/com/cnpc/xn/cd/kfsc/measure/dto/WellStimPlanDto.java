package com.cnpc.xn.cd.kfsc.measure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 措施计划DTO
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = "WellStimPlanDto", description = "措施计划传输对象")
public class WellStimPlanDto {

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty(value = "计划名称", required = true)
    @NotBlank(message = "计划名称不能为空")
    private String planName;

    @ApiModelProperty(value = "单位ID", required = true)
    @NotBlank(message = "单位ID不能为空")
    private String planOrgId;

    @ApiModelProperty("年度计划年月")
    private String planYearMonth;

    @ApiModelProperty("计划年份（年计划使用）")
    private String planYear;

    @ApiModelProperty(value = "计划类型", required = true)
    @NotBlank(message = "计划类型不能为空")
    private String planType;

    @ApiModelProperty("基础计划ID（月计划使用）")
    private String basePlanId;

    @ApiModelProperty("计划状态")
    private String planStatus;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("创建人ID")
    private String createUser;

    @ApiModelProperty("创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "计划明细列表", required = true)
    @NotEmpty(message = "计划明细不能为空")
    @Valid
    private List<WellStimDetailDto> details;
}
