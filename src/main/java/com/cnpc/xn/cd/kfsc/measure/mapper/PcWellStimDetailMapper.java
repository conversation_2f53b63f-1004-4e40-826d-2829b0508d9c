package com.cnpc.xn.cd.kfsc.measure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.kfsc.measure.dto.WellStimTrackingQueryDto;
import com.cnpc.xn.cd.kfsc.measure.dto.WellStimTrackingVo;
import com.cnpc.xn.cd.kfsc.measure.entity.PcWellStimDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 措施计划明细表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Mapper
public interface PcWellStimDetailMapper extends BaseMapper<PcWellStimDetail> {

    /**
     * 分页查询措施计划跟踪列表
     * 
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 分页结果
     */
    Page<WellStimTrackingVo> selectTrackingPage(Page<WellStimTrackingVo> page, @Param("query") WellStimTrackingQueryDto queryDto);
}
