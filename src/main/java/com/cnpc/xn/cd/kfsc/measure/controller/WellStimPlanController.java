package com.cnpc.xn.cd.kfsc.measure.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.common.core.domain.R;
import com.cnpc.xn.cd.kfsc.measure.dto.*;
import com.cnpc.xn.cd.kfsc.measure.entity.PcWellStimPlan;
import com.cnpc.xn.cd.kfsc.measure.service.WellStimPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 措施计划管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/well-stim-plan")
@Api(tags = "措施计划管理")
@Validated
public class WellStimPlanController {

    @Autowired
    private WellStimPlanService wellStimPlanService;

    /**
     * 分页查询措施计划跟踪列表
     */
    @PostMapping("/tracking/page")
    @ApiOperation("分页查询措施计划跟踪列表")
    public R<Page<WellStimTrackingVo>> getTrackingPage(@Valid @RequestBody WellStimTrackingQueryDto queryDto) {
        try {
            Page<WellStimTrackingVo> result = wellStimPlanService.getTrackingPage(queryDto);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询措施计划跟踪列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新跟踪信息
     */
    @PutMapping("/tracking/update")
    @ApiOperation("更新跟踪信息")
    public R<Boolean> updateTracking(@Valid @RequestBody WellStimTrackingUpdateDto updateDto) {
        try {
            boolean result = wellStimPlanService.updateTracking(updateDto);
            return R.ok(result, result ? "更新成功" : "更新失败");
        } catch (Exception e) {
            log.error("更新跟踪信息失败", e);
            return R.fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 创建年计划
     */
    @PostMapping("/annual/create")
    @ApiOperation("创建年计划")
    public R<Map<String, String>> createAnnualPlan(@Valid @RequestBody WellStimPlanDto planDto) {
        try {
            String planId = wellStimPlanService.createAnnualPlan(planDto);
            Map<String, String> result = new HashMap<>();
            result.put("planId", planId);
            return R.ok(result, "创建成功");
        } catch (Exception e) {
            log.error("创建年计划失败", e);
            return R.fail("创建失败：" + e.getMessage());
        }
    }

    /**
     * 查询年计划列表
     */
    @PostMapping("/annual/page")
    @ApiOperation("查询年计划列表")
    public R<Page<PcWellStimPlan>> getAnnualPlanPage(@RequestBody Map<String, Object> params) {
        try {
            Integer currentPage = (Integer) params.getOrDefault("currentPage", 1);
            Integer pageSize = (Integer) params.getOrDefault("pageSize", 10);
            String planYear = (String) params.get("planYear");
            String orgId = (String) params.get("orgId");
            String planStatus = (String) params.get("planStatus");
            
            Page<PcWellStimPlan> result = wellStimPlanService.getAnnualPlanPage(
                currentPage, pageSize, planYear, orgId, planStatus);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询年计划列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 提交年计划
     */
    @PutMapping("/annual/{planId}/submit")
    @ApiOperation("提交年计划")
    public R<Boolean> submitAnnualPlan(@PathVariable @NotBlank(message = "计划ID不能为空") String planId) {
        try {
            boolean result = wellStimPlanService.submitPlan(planId);
            return R.ok(result, result ? "提交成功" : "提交失败");
        } catch (Exception e) {
            log.error("提交年计划失败", e);
            return R.fail("提交失败：" + e.getMessage());
        }
    }

    /**
     * 创建月计划
     */
    @PostMapping("/monthly/create")
    @ApiOperation("创建月计划")
    public R<Map<String, String>> createMonthlyPlan(@Valid @RequestBody WellStimPlanDto planDto) {
        try {
            String planId = wellStimPlanService.createMonthlyPlan(planDto);
            Map<String, String> result = new HashMap<>();
            result.put("planId", planId);
            return R.ok(result, "创建成功");
        } catch (Exception e) {
            log.error("创建月计划失败", e);
            return R.fail("创建失败：" + e.getMessage());
        }
    }

    /**
     * 查询月计划列表
     */
    @PostMapping("/monthly/page")
    @ApiOperation("查询月计划列表")
    public R<Page<PcWellStimPlan>> getMonthlyPlanPage(@RequestBody Map<String, Object> params) {
        try {
            Integer currentPage = (Integer) params.getOrDefault("currentPage", 1);
            Integer pageSize = (Integer) params.getOrDefault("pageSize", 10);
            String planYearMonth = (String) params.get("planYearMonth");
            String orgId = (String) params.get("orgId");
            
            Page<PcWellStimPlan> result = wellStimPlanService.getMonthlyPlanPage(
                currentPage, pageSize, planYearMonth, orgId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询月计划列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 提交月计划
     */
    @PutMapping("/monthly/{planId}/submit")
    @ApiOperation("提交月计划")
    public R<Boolean> submitMonthlyPlan(@PathVariable @NotBlank(message = "计划ID不能为空") String planId) {
        try {
            boolean result = wellStimPlanService.submitPlan(planId);
            return R.ok(result, result ? "提交成功" : "提交失败");
        } catch (Exception e) {
            log.error("提交月计划失败", e);
            return R.fail("提交失败：" + e.getMessage());
        }
    }

    /**
     * 导入措施计划数据
     */
    @PostMapping("/import")
    @ApiOperation("导入措施计划数据")
    public R<Map<String, Object>> importData(
            @RequestParam("file") MultipartFile file,
            @RequestParam("planId") @ApiParam("计划ID") String planId) {
        try {
            Map<String, Object> result = wellStimPlanService.importData(file, planId);
            return R.ok(result, "导入完成");
        } catch (Exception e) {
            log.error("导入措施计划数据失败", e);
            return R.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出措施计划数据
     */
    @PostMapping("/export")
    @ApiOperation("导出措施计划数据")
    public void exportData(@RequestBody WellStimTrackingQueryDto queryDto, HttpServletResponse response) {
        try {
            wellStimPlanService.exportData(queryDto, response);
        } catch (Exception e) {
            log.error("导出措施计划数据失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取单位列表
     */
    @GetMapping("/basic/organizations")
    @ApiOperation("获取单位列表")
    public R<List<Map<String, Object>>> getOrganizations() {
        try {
            List<Map<String, Object>> result = wellStimPlanService.getOrganizations();
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取单位列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取井号列表
     */
    @GetMapping("/basic/wells")
    @ApiOperation("获取井号列表")
    public R<List<Map<String, Object>>> getWells(@RequestParam("orgId") @ApiParam("单位ID") String orgId) {
        try {
            List<Map<String, Object>> result = wellStimPlanService.getWells(orgId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取井号列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取措施类型列表
     */
    @GetMapping("/basic/stim-types")
    @ApiOperation("获取措施类型列表")
    public R<List<Map<String, Object>>> getStimTypes() {
        try {
            List<Map<String, Object>> result = wellStimPlanService.getStimTypes();
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取措施类型列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取已提交的年计划
     */
    @GetMapping("/basic/submitted-annual-plans")
    @ApiOperation("获取已提交的年计划")
    public R<List<Map<String, Object>>> getSubmittedAnnualPlans(
            @RequestParam(value = "orgId", required = false) @ApiParam("单位ID") String orgId) {
        try {
            List<Map<String, Object>> result = wellStimPlanService.getSubmittedAnnualPlans(orgId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取已提交的年计划失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }
}
