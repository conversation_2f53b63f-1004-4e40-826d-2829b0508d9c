package com.cnpc.xn.cd.kfsc.measure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 措施计划跟踪查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = "WellStimTrackingQueryDto", description = "措施计划跟踪查询条件")
public class WellStimTrackingQueryDto {

    @ApiModelProperty("当前页码")
    private Integer currentPage = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty("年度计划年月")
    private String planYearMonth;

    @ApiModelProperty("单位ID")
    private String orgId;

    @ApiModelProperty("井ID")
    private String wellId;

    @ApiModelProperty("措施类型ID")
    private String stimKeyId;

    @ApiModelProperty("开展情况动态")
    private String progressImplement;

    @ApiModelProperty("查询条件列表")
    private List<QueryCondition> queryConditions;

    @Data
    @ApiModel(value = "QueryCondition", description = "查询条件")
    public static class QueryCondition {
        
        @ApiModelProperty("查询参数类型")
        private String paramType;

        @ApiModelProperty("操作符：大于、等于、小于")
        private String operator;

        @ApiModelProperty("查询值")
        private String value;
    }
}
