<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.xn.cd.kfsc.measure.mapper.PcWellStimDetailMapper">

    <!-- 分页查询措施计划跟踪列表 -->
    <select id="selectTrackingPage" resultType="com.cnpc.xn.cd.kfsc.measure.dto.WellStimTrackingVo">
        SELECT 
            d.DETAIL_ID as detailId,
            d.PLAN_ID as planId,
            ROW_NUMBER() OVER (ORDER BY d.UPDATE_DATE DESC) as serialNumber,
            p.PLAN_YEAR_MONTH as planYearMonth,
            d.MONTH_NUM as monthNum,
            o.ORG_NAME as orgName,
            '开发' as domain,
            w.WELL_NAME as wellName,
            s.STIM_TYPE_NAME as stimTypeName,
            '自筹' as fundingChannel,
            d.PROGRESS_IMPLEMENT as progressImplement,
            d.CONSTRUCT_START_DATE as constructStartDate,
            d.CONSTRUCT_END_DATE as constructEndDate,
            d.COMPLETION_DATE as completionDate,
            d.GAS_PROD_PRE_STIM_DAILY as gasProductionPreStimDaily,
            d.GAS_PROD_AFT_STIM_DAILY as gasProductionAftStimDaily,
            d.REMARKS as remarks,
            u.USER_NAME as maintainerName,
            d.UPDATE_DATE as updateDate
        FROM PC_WELL_STIM_DETAIL d
        LEFT JOIN PC_WELL_STIM_PLAN p ON d.PLAN_ID = p.PLAN_ID
        LEFT JOIN CD_ORGANIZATION o ON p.PLAN_ORG_ID = o.ORG_ID
        LEFT JOIN CD_WELL w ON d.WELL_ID = w.WELL_ID
        LEFT JOIN PK_STIM_TYPE s ON d.STIM_KEY_ID = s.STIM_KEY_ID
        LEFT JOIN SYS_USER u ON d.UPDATE_USER = u.USER_ID
        <where>
            <if test="query.planYearMonth != null and query.planYearMonth != ''">
                AND p.PLAN_YEAR_MONTH = #{query.planYearMonth}
            </if>
            <if test="query.orgId != null and query.orgId != ''">
                AND p.PLAN_ORG_ID = #{query.orgId}
            </if>
            <if test="query.wellId != null and query.wellId != ''">
                AND d.WELL_ID = #{query.wellId}
            </if>
            <if test="query.stimKeyId != null and query.stimKeyId != ''">
                AND d.STIM_KEY_ID = #{query.stimKeyId}
            </if>
            <if test="query.progressImplement != null and query.progressImplement != ''">
                AND d.PROGRESS_IMPLEMENT = #{query.progressImplement}
            </if>
        </where>
        ORDER BY d.UPDATE_DATE DESC
    </select>

</mapper>
