# 措施计划管理接口文档

## 1. 概述

措施计划管理系统用于管理年计划、月计划和实施跟踪，包含措施工艺计划主表(PC_WELL_STIM_PLAN)和措施计划明细表(PC_WELL_STIM_DETAIL)。

### 技术栈
- 前端：Vue3
- 后端：JDK8 + Spring Boot
- 持久框架：MyBatis-Plus
- 数据库：Oracle
- 数据库连接池：Druid

### 数据表结构

#### PC_WELL_STIM_PLAN (措施工艺计划主表)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| PLAN_ID | VARCHAR2(64) | 措施计划主键ID |
| PLAN_NAME | VARCHAR2(200) | 计划名称 |
| PLAN_ORG_ID | VARCHAR2(64) | 单位ID |
| PLAN_YEAR_MONTH | VARCHAR2(6) | 年度计划年月(如202501) |
| PLAN_TYPE | VARCHAR2(10) | 计划类型 |
| PLAN_STATUS | VARCHAR2(1) | 计划状态：0保存未提交，1已提交审核中，2审核通过，5审核未通过 |
| REMARKS | VARCHAR2(500) | 备注 |
| CREATE_USER | VARCHAR2(64) | 创建人ID |
| CREATE_DATE | TIMESTAMP | 创建日期 |
| UPDATE_USER | VARCHAR2(64) | 更新用户ID |
| UPDATE_DATE | TIMESTAMP | 更新日期 |

#### PC_WELL_STIM_DETAIL (措施计划明细表)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| DETAIL_ID | VARCHAR2(64) | 明细ID |
| PLAN_ID | VARCHAR2(64) | 措施计划主键ID |
| MONTH_NUM | VARCHAR2(2) | 预计开工月份 |
| WELL_ID | VARCHAR2(64) | 井ID |
| STIM_KEY_ID | VARCHAR2(64) | 措施分类代码 |
| PLAN_IMPLEMENT_TIME | TIMESTAMP | 措施计划实施时间 |
| CONSTRUCT_START_DATE | TIMESTAMP | 实际开工日期 |
| CONSTRUCT_END_DATE | TIMESTAMP | 施工完成日期 |
| COMPLETION_DATE | TIMESTAMP | 采气工艺投运日期 |
| GAS_PROD_PRE_STIM_DAILY | NUMBER(10,2) | 措施前日产气量 |
| GAS_PROD_AFT_STIM_DAILY | NUMBER(10,2) | 措施后日产气量 |
| PROGRESS_IMPLEMENT | VARCHAR2(20) | 开展情况动态 |
| CHANGE_TYPE | VARCHAR2(20) | 安排类型 |
| REMARKS | VARCHAR2(500) | 备注 |
| CREATE_USER | VARCHAR2(64) | 创建用户ID |
| CREATE_DATE | TIMESTAMP | 创建时间 |
| UPDATE_USER | VARCHAR2(64) | 更新用户ID |
| UPDATE_DATE | TIMESTAMP | 更新时间 |

## 2. 通用响应格式

所有接口均采用统一的响应格式：

```json
{
    "code": 0,
    "data": {},
    "msg": ""
}
```

- `code`: 状态码，0表示成功，其他表示失败
- `data`: 响应数据
- `msg`: 响应消息，成功时为空，失败时包含错误信息

### 分页响应格式

```json
{
    "code": 0,
    "data": {
        "currentPage": 1,
        "pageSize": 10,
        "totalCount": 100,
        "totalPage": 10,
        "records": []
    },
    "msg": ""
}
```

## 3. 接口列表

### 3.1 查询跟踪页面

#### 3.1.1 分页查询措施计划跟踪列表

**接口地址：** `POST /api/v1/well-stim-plan/tracking/page`

**请求参数：**

```json
{
    "currentPage": 1,
    "pageSize": 10,
    "planYearMonth": "202501",
    "orgId": "ORG001",
    "wellId": "WELL001",
    "stimKeyId": "STIM001",
    "progressImplement": "进行中",
    "queryConditions": [
        {
            "paramType": "实际开工日期",
            "operator": "大于",
            "value": "2025-01-01"
        }
    ]
}
```

**请求参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| currentPage | Integer | 是 | 当前页码 |
| pageSize | Integer | 是 | 每页大小 |
| planYearMonth | String | 否 | 年度计划年月 |
| orgId | String | 否 | 单位ID |
| wellId | String | 否 | 井号 |
| stimKeyId | String | 否 | 措施类型 |
| progressImplement | String | 否 | 开展情况动态 |
| queryConditions | Array | 否 | 查询条件数组 |

**queryConditions参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| paramType | String | 是 | 查询参数类型 |
| operator | String | 是 | 操作符：大于、等于、小于 |
| value | String | 是 | 查询值 |

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "currentPage": 1,
        "pageSize": 10,
        "totalCount": 50,
        "totalPage": 5,
        "records": [
            {
                "detailId": "DETAIL001",
                "planId": "PLAN001",
                "serialNumber": 1,
                "planYearMonth": "202501",
                "monthNum": "01",
                "orgName": "页岩气公司",
                "domain": "开发",
                "wellName": "井001",
                "stimTypeName": "压裂",
                "fundingChannel": "自筹",
                "progressImplement": "进行中",
                "constructStartDate": "2025-01-15",
                "constructEndDate": "2025-01-20",
                "completionDate": "2025-01-25",
                "gasProductionPreStimDaily": 1000.50,
                "gasProductionAftStimDaily": 1500.75,
                "remarks": "正常施工",
                "maintainerName": "张三",
                "updateDate": "2025-01-10 10:30:00"
            }
        ]
    },
    "msg": ""
}
```

#### 3.1.2 更新跟踪信息

**接口地址：** `PUT /api/v1/well-stim-plan/tracking/update`

**请求参数：**

```json
{
    "detailId": "DETAIL001",
    "progressImplement": "已完成",
    "constructStartDate": "2025-01-15",
    "constructEndDate": "2025-01-20",
    "completionDate": "2025-01-25",
    "gasProductionPreStimDaily": 1000.50,
    "gasProductionAftStimDaily": 1500.75,
    "remarks": "施工完成"
}
```

**响应示例：**

```json
{
    "code": 0,
    "data": true,
    "msg": "更新成功"
}
```

### 3.2 数据导入导出

#### 3.2.1 导入措施计划数据

**接口地址：** `POST /api/v1/well-stim-plan/import`

**请求参数：** 文件上传（multipart/form-data）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | Excel文件 |
| planId | String | 是 | 计划ID |

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "successCount": 10,
        "failCount": 2,
        "errorMessages": [
            "第3行：井号不存在",
            "第5行：措施类型不正确"
        ]
    },
    "msg": "导入完成"
}
```

#### 3.2.2 导出措施计划数据

**接口地址：** `POST /api/v1/well-stim-plan/export`

**请求参数：**

```json
{
    "planYearMonth": "202501",
    "orgId": "ORG001",
    "exportType": "tracking"
}
```

**响应：** 文件下载

### 3.3 年计划管理

#### 3.3.1 创建年计划

**接口地址：** `POST /api/v1/well-stim-plan/annual/create`

**请求参数：**

```json
{
    "planName": "2025年度措施计划",
    "planOrgId": "ORG001",
    "planYear": "2025",
    "planType": "ANNUAL",
    "remarks": "年度计划",
    "details": [
        {
            "monthNum": "01",
            "wellId": "WELL001",
            "stimKeyId": "STIM001",
            "planImplementTime": "2025-01-15 10:00:00"
        }
    ]
}
```

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "planId": "PLAN001"
    },
    "msg": "创建成功"
}
```

#### 3.3.2 查询年计划列表

**接口地址：** `POST /api/v1/well-stim-plan/annual/page`

**请求参数：**

```json
{
    "currentPage": 1,
    "pageSize": 10,
    "planYear": "2025",
    "orgId": "ORG001",
    "planStatus": "2"
}
```

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "currentPage": 1,
        "pageSize": 10,
        "totalCount": 20,
        "totalPage": 2,
        "records": [
            {
                "planId": "PLAN001",
                "planName": "2025年度措施计划",
                "orgName": "页岩气公司",
                "planYear": "2025",
                "planStatus": "2",
                "planStatusName": "审核通过",
                "createUserName": "李四",
                "createDate": "2025-01-01 09:00:00",
                "detailCount": 15
            }
        ]
    },
    "msg": ""
}
```

#### 3.3.3 提交年计划

**接口地址：** `PUT /api/v1/well-stim-plan/annual/{planId}/submit`

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| planId | String | 是 | 计划ID |

**响应示例：**

```json
{
    "code": 0,
    "data": true,
    "msg": "提交成功"
}
```

### 3.4 月计划管理

#### 3.4.1 创建月计划

**接口地址：** `POST /api/v1/well-stim-plan/monthly/create`

**请求参数：**

```json
{
    "planName": "2025年01月措施计划",
    "planOrgId": "ORG001",
    "planYearMonth": "202501",
    "planType": "MONTHLY",
    "basePlanId": "PLAN001",
    "remarks": "月度计划",
    "details": [
        {
            "wellId": "WELL001",
            "stimKeyId": "STIM001",
            "planImplementTime": "2025-01-15 10:00:00",
            "changeType": "新增"
        }
    ]
}
```

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "planId": "PLAN002"
    },
    "msg": "创建成功"
}
```

#### 3.4.2 查询月计划列表

**接口地址：** `POST /api/v1/well-stim-plan/monthly/page`

**请求参数：**

```json
{
    "currentPage": 1,
    "pageSize": 10,
    "planYearMonth": "202501",
    "orgId": "ORG001"
}
```

**响应示例：**

```json
{
    "code": 0,
    "data": {
        "currentPage": 1,
        "pageSize": 10,
        "totalCount": 12,
        "totalPage": 2,
        "records": [
            {
                "planId": "PLAN002",
                "planName": "2025年01月措施计划",
                "orgName": "页岩气公司",
                "planYearMonth": "202501",
                "planStatus": "1",
                "planStatusName": "已提交审核中",
                "createUserName": "王五",
                "createDate": "2025-01-05 14:00:00",
                "detailCount": 8
            }
        ]
    },
    "msg": ""
}
```

### 3.5 基础数据接口

#### 3.5.1 获取单位列表

**接口地址：** `GET /api/v1/well-stim-plan/basic/organizations`

**响应示例：**

```json
{
    "code": 0,
    "data": [
        {
            "orgId": "ORG001",
            "orgName": "页岩气公司",
            "domain": "开发"
        }
    ],
    "msg": ""
}
```

#### 3.5.2 获取井号列表

**接口地址：** `GET /api/v1/well-stim-plan/basic/wells`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orgId | String | 是 | 单位ID |

**响应示例：**

```json
{
    "code": 0,
    "data": [
        {
            "wellId": "WELL001",
            "wellName": "井001"
        }
    ],
    "msg": ""
}
```

#### 3.5.3 获取措施类型列表

**接口地址：** `GET /api/v1/well-stim-plan/basic/stim-types`

**响应示例：**

```json
{
    "code": 0,
    "data": [
        {
            "stimKeyId": "STIM001",
            "stimTypeName": "压裂"
        }
    ],
    "msg": ""
}
```

#### 3.5.4 获取已提交的年计划

**接口地址：** `GET /api/v1/well-stim-plan/basic/submitted-annual-plans`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orgId | String | 否 | 单位ID |

**响应示例：**

```json
{
    "code": 0,
    "data": [
        {
            "planId": "PLAN001",
            "planName": "2025年度措施计划",
            "planYear": "2025",
            "orgId": "ORG001",
            "orgName": "页岩气公司"
        }
    ],
    "msg": ""
}
```

## 4. 状态码说明

### 4.1 通用状态码

| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 50003 | 参数有误 |
| 50004 | 参数不完整 |
| 50006 | 数据库连接异常 |
| 50008 | 服务端错误 |
| 50010 | 无数据 |
| 50011 | 记录已存在 |
| 50013 | 修改错误 |

### 4.2 业务状态码

| 状态码 | 说明 |
|--------|------|
| 60001 | 计划不存在 |
| 60002 | 计划已提交，不允许修改 |
| 60003 | 年份+单位已存在 |
| 60004 | 井号不存在 |
| 60005 | 措施类型不存在 |
| 60006 | 文件格式不正确 |

## 5. 数据字典

### 5.1 计划状态 (PLAN_STATUS)

| 值 | 说明 |
|----|------|
| 0 | 保存未提交 |
| 1 | 已提交审核中 |
| 2 | 审核通过 |
| 5 | 审核未通过 |

### 5.2 计划类型 (PLAN_TYPE)

| 值 | 说明 |
|----|------|
| ANNUAL | 年计划 |
| MONTHLY | 月计划 |

### 5.3 开展情况动态 (PROGRESS_IMPLEMENT)

| 值 | 说明 |
|----|------|
| 待开采 | 待开采 |
| 进行中 | 进行中 |
| 已完成 | 已完成 |

### 5.4 安排类型 (CHANGE_TYPE)

| 值 | 说明 |
|----|------|
| 新增 | 新增 |
| 取消实施 | 取消实施 |
| 延后实施 | 延后实施 |

## 6. 错误处理

### 6.1 参数校验错误

当请求参数不符合要求时，返回400状态码和详细错误信息：

```json
{
    "code": 50003,
    "data": null,
    "msg": "参数校验失败：planYearMonth格式不正确，应为YYYYMM格式"
}
```

### 6.2 业务逻辑错误

当业务逻辑校验失败时，返回对应的业务状态码：

```json
{
    "code": 60002,
    "data": null,
    "msg": "计划已提交，不允许修改"
}
```

### 6.3 系统错误

当系统发生异常时，返回500状态码：

```json
{
    "code": 50008,
    "data": null,
    "msg": "服务端错误，请联系管理员"
}
```

## 7. 注意事项

1. 所有时间格式统一为 `yyyy-MM-dd HH:mm:ss`
2. 年份+单位作为年计划的唯一标识
3. 计划发布状态后不允许再编辑
4. 月计划只能基于已提交的年计划创建
5. 导入文件格式必须为Excel(.xlsx)
6. 所有金额和数量字段保留2位小数
7. 接口调用需要携带有效的JWT Token
8. 分页查询默认按更新时间倒序排列
