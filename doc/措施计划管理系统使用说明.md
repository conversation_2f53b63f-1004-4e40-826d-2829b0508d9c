# 措施计划管理系统使用说明

## 概述

措施计划管理系统是用于管理油气田措施工艺计划的综合管理平台，包含年计划管理、月计划管理和实施跟踪三个主要功能模块。

## 功能模块

### 1. 年计划管理
- **功能描述**：创建和管理年度措施计划
- **主要特点**：
  - 年份+单位作为唯一标识
  - 支持计划状态管理（保存、提交、审核）
  - 发布状态后不允许再编辑

### 2. 月计划管理
- **功能描述**：基于已审核通过的年计划创建月度计划
- **主要特点**：
  - 必须基于已提交的年计划
  - 支持新增、取消实施、延后实施等安排类型
  - 可以对年计划进行细化调整

### 3. 实施跟踪
- **功能描述**：跟踪措施计划的实施进度和效果
- **主要特点**：
  - 支持多条件查询
  - 可录入实际开工日期、完成日期等关键信息
  - 记录措施前后产气量对比

## 使用流程

### 年计划创建流程
1. 选择单位和年份
2. 添加计划明细（井号、措施类型、预计实施时间等）
3. 保存计划
4. 提交审核
5. 等待审核通过

### 月计划创建流程
1. 选择已审核通过的年计划
2. 选择具体年月（如202501）
3. 基于年计划添加或调整明细
4. 设置安排类型（新增/取消实施/延后实施）
5. 提交审核

### 实施跟踪流程
1. 查询需要跟踪的计划明细
2. 录入实际开工日期
3. 更新开展情况动态
4. 录入施工完成日期和投运日期
5. 记录措施前后产气量

## 接口使用示例

### 1. 创建年计划

```bash
POST /api/v1/well-stim-plan/annual/create
Content-Type: application/json

{
    "planName": "2025年度措施计划",
    "planOrgId": "ORG001",
    "planYear": "2025",
    "planType": "ANNUAL",
    "remarks": "年度计划",
    "details": [
        {
            "monthNum": "01",
            "wellId": "WELL001",
            "stimKeyId": "STIM001",
            "planImplementTime": "2025-01-15 10:00:00"
        }
    ]
}
```

### 2. 查询跟踪列表

```bash
POST /api/v1/well-stim-plan/tracking/page
Content-Type: application/json

{
    "currentPage": 1,
    "pageSize": 10,
    "planYearMonth": "202501",
    "orgId": "ORG001",
    "progressImplement": "进行中"
}
```

### 3. 更新跟踪信息

```bash
PUT /api/v1/well-stim-plan/tracking/update
Content-Type: application/json

{
    "detailId": "DETAIL001",
    "progressImplement": "已完成",
    "constructStartDate": "2025-01-15",
    "constructEndDate": "2025-01-20",
    "completionDate": "2025-01-25",
    "gasProductionPreStimDaily": 1000.50,
    "gasProductionAftStimDaily": 1500.75
}
```

## 数据字典

### 计划状态
- `0`: 保存未提交
- `1`: 已提交审核中  
- `2`: 审核通过
- `5`: 审核未通过

### 计划类型
- `ANNUAL`: 年计划
- `MONTHLY`: 月计划

### 开展情况动态
- `待开采`: 待开采
- `进行中`: 进行中
- `已完成`: 已完成

### 安排类型
- `新增`: 新增
- `取消实施`: 取消实施
- `延后实施`: 延后实施

## 注意事项

1. **权限控制**：所有接口调用需要携带有效的JWT Token
2. **数据校验**：
   - 年份格式：YYYY（如2025）
   - 年月格式：YYYYMM（如202501）
   - 日期格式：yyyy-MM-dd HH:mm:ss
3. **业务规则**：
   - 年份+单位的组合必须唯一
   - 计划提交后状态变更需要审核
   - 月计划必须基于已审核通过的年计划
4. **性能优化**：
   - 分页查询建议每页不超过100条
   - 导出数据建议限制在10000条以内
5. **错误处理**：
   - 所有接口都有统一的错误响应格式
   - 业务异常会返回具体的错误码和消息

## 技术支持

如有技术问题，请联系开发团队或查看详细的接口文档。
